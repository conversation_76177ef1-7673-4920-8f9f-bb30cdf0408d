"use client";

import Image from "next/image";
import { Link } from "@/i18n/navigation";
import { usePathname, useRouter } from "@/i18n/navigation";
import { useLocale, useTranslations } from "next-intl";
import { Home, Users, LogOut, Globe, PanelLeft } from "lucide-react";
import { cn } from "@/lib/utils";
import { dtcAssets } from "@/lib/assets";
import { useAuth } from "@/hooks/useAuth";
import { useUser } from "@/hooks/useUser";
import { toast } from "sonner";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  useSidebar,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";

interface NavigationItem {
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles: string[];
}

interface DtcSidebarProps {
  className?: string;
}

export function DtcSidebar({ className }: DtcSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations("nav");
  const { signOut } = useAuth();
  const { user } = useUser();
  const { toggleSidebar, state } = useSidebar();
  const firstName = (user?.displayName?.split(" ")[0] || user?.email?.split("@")[0] || "");

  // Navigation items based on user roles
  const navigationItems: NavigationItem[] = [
    {
      label: t("home"),
      href: "/dashboard",
      icon: Home,
      roles: ["Admin", "DTC"],
    },
    {
      label: t("userManagement"),
      href: "/dashboard/users",
      icon: Users,
      roles: ["Admin"],
    },
  ];

  // Filter navigation items based on user role
  const filteredNavItems = navigationItems.filter(item =>
    user?.role && item.roles.includes(user.role)
  );

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success("Signed out successfully");
      router.push("/login");
    } catch (error) {
      console.error("Sign out error:", error);
      toast.error("Failed to sign out");
    }
  };

  const switchLanguage = () => {
    const newLocale = locale === "en" ? "ar" : "en";
    // Use window.location for a full page refresh to ensure RTL changes take effect
    const currentPath = pathname.startsWith('/dashboard') ? pathname : '/dashboard';
    window.location.href = `/${newLocale}${currentPath}`;
  };

  return (
    <Sidebar
      collapsible="icon"
      side={locale === "ar" ? "right" : "left"}
      className={cn("border-0 bg-gradient-to-b from-primary to-primary-deep", className)}
    >
      <SidebarHeader className="border-b border-white/10">
        {state === "collapsed" ? (
          <div className="flex flex-col items-center gap-3 py-4">
            <div className="sidebar-logo bg-white/10 backdrop-blur-sm rounded-md p-2 border border-white/20 shadow-sm">
              <Image
                src={dtcAssets.logoBlack}
                alt="DTC Logo"
                width={28}
                height={28}
                className="object-contain brightness-0 invert"
                priority
              />
            </div>

            {user?.photoURL ? (
              <Image
                src={user.photoURL}
                alt="User Avatar"
                width={36}
                height={36}
                className="rounded-full border border-white/40 shadow-sm"
              />
            ) : (
              <div className="w-9 h-9 bg-white/20 rounded-full flex items-center justify-center border border-white/40 shadow-sm">
                <span className="text-white font-semibold text-base font-display">
                  {user?.displayName?.charAt(0) || user?.email?.charAt(0) || ""}
                </span>
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center gap-4 py-5">
            <div className="sidebar-logo bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/20 shadow-sm">
              <Image
                src={dtcAssets.logoBlack}
                alt="DTC Logo"
                width={120}
                height={36}
                className="object-contain brightness-0 invert"
                priority
              />
            </div>
            <div className="flex flex-col items-center gap-1">
              {user?.photoURL ? (
                <Image
                  src={user.photoURL}
                  alt="User Avatar"
                  width={48}
                  height={48}
                  className="rounded-full border border-white/40 shadow-sm"
                />
              ) : (
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center border border-white/40 shadow-sm">
                  <span className="text-white font-semibold text-lg font-display">
                    {user?.displayName?.charAt(0) || user?.email?.charAt(0) || ""}
                  </span>
                </div>
              )}
              <p className="text-white/90 text-sm font-medium leading-none mt-1">
                {firstName}
              </p>
            </div>
          </div>
        )}
      </SidebarHeader>

      <SidebarContent className="px-3 py-4">
        <SidebarMenu className="gap-2">
          {filteredNavItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <SidebarMenuItem key={item.href}>
                <SidebarMenuButton
                  asChild
                  isActive={isActive}
                  size="lg"
                  tooltip={{ children: item.label, className: "bg-black/80 text-white border-white/20 backdrop-blur-sm" }}
                  className="sidebar-nav-btn relative text-white/90 hover:text-white bg-transparent rounded-lg hover:bg-white/15 border border-transparent hover:border-white/25 data-[active=true]:bg-white/20 data-[active=true]:border-white/30 data-[active=true]:shadow-md transition-all duration-200 ease-out hover:-translate-y-0.5"
                >
                  <Link href={item.href} className="flex items-center gap-3">
                    <item.icon className="w-5 h-5" />
                    <span className="sidebar-text text-sm font-medium">{item.label}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter className="border-t border-white/10 p-2">
        <SidebarMenu className="gap-2">
          <SidebarMenuItem>
            <SidebarMenuButton
              tooltip={{ children: t("switchLanguage"), className: "bg-black/80 text-white border-white/20 backdrop-blur-sm" }}
              onClick={switchLanguage}
              size="lg"
              className="text-white/90 hover:text-white bg-transparent rounded-lg hover:bg-white/15 border border-transparent hover:border-white/25"
            >
              <Globe className="w-4 h-4" />
              <span className="sidebar-text text-sm font-medium">{t("switchLanguage")}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              tooltip={{ children: t("minimize"), className: "bg-black/80 text-white border-white/20 backdrop-blur-sm" }}
              onClick={toggleSidebar}
              size="lg"
              className="text-white/90 hover:text-white bg-transparent rounded-lg hover:bg-white/15 border border-transparent hover:border-white/25"
            >
              <PanelLeft className="w-4 h-4" />
              <span className="sidebar-text text-sm font-medium">{t("minimize")}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              tooltip={{ children: t("signOut"), className: "bg-black/80 text-white border-white/20 backdrop-blur-sm" }}
              onClick={handleSignOut}
              size="lg"
              className="text-white/90 hover:text-white bg-transparent rounded-lg hover:bg-white/15 border border-transparent hover:border-white/25"
            >
              <LogOut className="w-4 h-4" />
              <span className="sidebar-text text-sm font-medium">{t("signOut")}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}