"use client";

import { useState, useEffect } from "react";
import { DtcModal } from "@/components/ui/DtcModal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User, Mail, Lock, Image, Phone } from "lucide-react";
import { type UserProfile } from "@/Services/userService";

interface UserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (userData: UserFormData) => Promise<void>;
  user?: UserProfile | null;
  mode: 'create' | 'edit';
}

export interface UserFormData {
  email: string;
  password?: string;
  firstName: string;
  lastName: string;
  displayName: string;
  photoURL: string;
  role: 'Admin' | 'DTC';
  phoneNumber?: string;
}

export function UserModal({ isOpen, onClose, onSave, user, mode }: UserModalProps) {
  const [formData, setFormData] = useState<UserFormData>({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    displayName: '',
    photoURL: '',
    role: 'DTC',
    phoneNumber: '',
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (user && mode === 'edit') {
      setFormData({
        email: user.email || '',
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        displayName: user.displayName || '',
        photoURL: user.photoURL || '',
        role: user.role,
        phoneNumber: user.phoneNumber || '',
      });
    } else {
      setFormData({
        email: '',
        password: '',
        firstName: '',
        lastName: '',
        displayName: '',
        photoURL: '',
        role: 'DTC',
        phoneNumber: '',
      });
    }
    setErrors({});
  }, [user, mode, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (mode === 'create' && !formData.password) {
      newErrors.password = 'Password is required';
    }

    if (mode === 'create' && formData.password && formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (!formData.firstName) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName) {
      newErrors.lastName = 'Last name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Error saving user:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof UserFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-generate display name from first and last name
    if (field === 'firstName' || field === 'lastName') {
      const firstName = field === 'firstName' ? value : formData.firstName;
      const lastName = field === 'lastName' ? value : formData.lastName;
      setFormData(prev => ({ 
        ...prev, 
        [field]: value,
        displayName: `${firstName} ${lastName}`.trim()
      }));
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const getInitials = () => {
    if (formData.firstName && formData.lastName) {
      return `${formData.firstName[0]}${formData.lastName[0]}`.toUpperCase();
    }
    return formData.email[0]?.toUpperCase() || 'U';
  };

  return (
    <DtcModal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Create New User' : 'Edit User'}
      description={mode === 'create' ? 'Add a new user to the DTC Portal' : 'Update user information'}
      size="lg"
      footer={
        <>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
            className="border-border/50 text-grey hover:text-charcoal hover:bg-muted/50 transition-colors"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading}
            className="bg-primary hover:bg-primary-deep text-white shadow-sm transition-all hover:shadow-md"
          >
            {loading ? 'Saving...' : mode === 'create' ? 'Create User' : 'Update User'}
          </Button>
        </>
      }
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Profile Picture Preview */}
        <div className="flex justify-center">
          <Avatar className="h-20 w-20 ring-4 ring-primary/10 shadow-sm">
            <AvatarImage src={formData.photoURL || undefined} />
            <AvatarFallback className="bg-primary/10 text-primary text-lg font-medium">
              {getInitials()}
            </AvatarFallback>
          </Avatar>
        </div>

        <div className="grid grid-cols-2 gap-4">
          {/* First Name */}
          <div className="space-y-2">
            <Label htmlFor="firstName" className="text-charcoal font-medium">
              First Name *
            </Label>
            <div className="relative">
              <User className="absolute left-3 top-3 h-4 w-4 text-grey" />
              <Input
                id="firstName"
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                className={`pl-10 border-border/50 focus:border-primary transition-colors ${errors.firstName ? 'border-red-400 focus:border-red-400' : ''}`}
                placeholder="Enter first name"
              />
            </div>
            {errors.firstName && (
              <p className="text-sm text-red-600">{errors.firstName}</p>
            )}
          </div>

          {/* Last Name */}
          <div className="space-y-2">
            <Label htmlFor="lastName" className="text-charcoal font-medium">
              Last Name *
            </Label>
            <div className="relative">
              <User className="absolute left-3 top-3 h-4 w-4 text-grey" />
              <Input
                id="lastName"
                value={formData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                className={`pl-10 border-border/50 focus:border-primary transition-colors ${errors.lastName ? 'border-red-400 focus:border-red-400' : ''}`}
                placeholder="Enter last name"
              />
            </div>
            {errors.lastName && (
              <p className="text-sm text-red-600">{errors.lastName}</p>
            )}
          </div>
        </div>

        {/* Display Name */}
        <div className="space-y-2">
          <Label htmlFor="displayName" className="text-charcoal font-medium">
            Display Name
          </Label>
          <Input
            id="displayName"
            value={formData.displayName}
            onChange={(e) => handleInputChange('displayName', e.target.value)}
            className="border-border/50 focus:border-primary transition-colors"
            placeholder="Auto-generated from first and last name"
          />
        </div>

        {/* Email */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-charcoal font-medium">
            Email Address *
          </Label>
          <div className="relative">
            <Mail className="absolute left-3 top-3 h-4 w-4 text-grey" />
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className={`pl-10 border-border/50 focus:border-primary transition-colors ${errors.email ? 'border-red-400 focus:border-red-400' : ''} ${mode === 'edit' ? 'bg-muted/30' : ''}`}
              placeholder="Enter email address"
              disabled={mode === 'edit'}
            />
          </div>
          {errors.email && (
            <p className="text-sm text-red-600">{errors.email}</p>
          )}
        </div>

        {/* Password (only for create mode) */}
        {mode === 'create' && (
          <div className="space-y-2">
            <Label htmlFor="password" className="text-charcoal font-medium">
              Password *
            </Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-grey" />
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className={`pl-10 border-border/50 focus:border-primary transition-colors ${errors.password ? 'border-red-400 focus:border-red-400' : ''}`}
                placeholder="Enter password (min. 6 characters)"
              />
            </div>
            {errors.password && (
              <p className="text-sm text-red-600">{errors.password}</p>
            )}
          </div>
        )}

        <div className="grid grid-cols-2 gap-4">
          {/* Role */}
          <div className="space-y-2">
            <Label htmlFor="role" className="text-charcoal font-medium">
              Role
            </Label>
            <Select
              value={formData.role}
              onValueChange={(value: 'Admin' | 'DTC') => handleInputChange('role', value)}
            >
              <SelectTrigger className="border-border/50 focus:border-primary transition-colors">
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent className="bg-white border-border/50 shadow-lg">
                <SelectItem value="DTC" className="hover:bg-primary/5 focus:bg-primary/5">DTC</SelectItem>
                <SelectItem value="Admin" className="hover:bg-primary/5 focus:bg-primary/5">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Phone Number */}
          <div className="space-y-2">
            <Label htmlFor="phoneNumber" className="text-charcoal font-medium">
              Phone Number <span className="text-grey text-sm">(optional)</span>
            </Label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 h-4 w-4 text-grey" />
              <Input
                id="phoneNumber"
                value={formData.phoneNumber || ''}
                onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                className="pl-10 border-border/50 focus:border-primary transition-colors"
                placeholder="Enter phone number (optional)"
              />
            </div>
          </div>
        </div>

        {/* Profile Picture URL */}
        <div className="space-y-2">
          <Label htmlFor="photoURL" className="text-charcoal font-medium">
            Profile Picture URL
          </Label>
          <div className="relative">
            <Image className="absolute left-3 top-3 h-4 w-4 text-grey" />
            <Input
              id="photoURL"
              value={formData.photoURL}
              onChange={(e) => handleInputChange('photoURL', e.target.value)}
              className="pl-10 border-border/50 focus:border-primary transition-colors"
              placeholder="Enter image URL (optional)"
            />
          </div>
        </div>
      </form>
    </DtcModal>
  );
}
