"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Edit, Trash2, MoreHorizontal, Users } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { type UserProfile } from "@/Services/userService";

interface UserTableProps {
  users: UserProfile[];
  onEditUser: (user: UserProfile) => void;
  onDeleteUser: (user: UserProfile) => void;
  loading?: boolean;
}

export function UserTable({ users, onEditUser, onDeleteUser, loading = false }: UserTableProps) {
  const [sortField, setSortField] = useState<keyof UserProfile>('displayName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const sortedUsers = [...users].sort((a, b) => {
    const aValue = a[sortField] || '';
    const bValue = b[sortField] || '';
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const handleSort = (field: keyof UserProfile) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Never';
    const d = date.toDate ? date.toDate() : new Date(date);
    return d.toLocaleDateString();
  };

  const getInitials = (user: UserProfile) => {
    if (user.firstName && user.lastName) {
      return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
    }
    if (user.displayName) {
      const names = user.displayName.split(' ');
      return names.length > 1 
        ? `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase()
        : names[0][0].toUpperCase();
    }
    return user.email?.[0]?.toUpperCase() || 'U';
  };

  if (loading) {
    return (
      <div className="rounded-lg bg-white shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-border/50">
              <TableHead className="text-charcoal font-medium">User</TableHead>
              <TableHead className="text-charcoal font-medium">Email</TableHead>
              <TableHead className="text-charcoal font-medium">Role</TableHead>
              <TableHead className="text-charcoal font-medium">Status</TableHead>
              <TableHead className="text-charcoal font-medium">Last Login</TableHead>
              <TableHead className="w-[100px] text-charcoal font-medium">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[...Array(5)].map((_, i) => (
              <TableRow key={i} className="border-b border-border/30 hover:bg-muted/30 transition-colors">
                <TableCell className="py-4">
                  <div className="flex items-center space-x-3">
                    <div className="h-8 w-8 bg-primary/10 rounded-full animate-pulse" />
                    <div className="space-y-1">
                      <div className="h-4 w-24 bg-primary/10 rounded animate-pulse" />
                      <div className="h-3 w-16 bg-primary/10 rounded animate-pulse" />
                    </div>
                  </div>
                </TableCell>
                <TableCell className="py-4">
                  <div className="h-4 w-32 bg-primary/10 rounded animate-pulse" />
                </TableCell>
                <TableCell className="py-4">
                  <div className="h-6 w-16 bg-primary/10 rounded animate-pulse" />
                </TableCell>
                <TableCell className="py-4">
                  <div className="h-6 w-20 bg-primary/10 rounded animate-pulse" />
                </TableCell>
                <TableCell className="py-4">
                  <div className="h-4 w-20 bg-primary/10 rounded animate-pulse" />
                </TableCell>
                <TableCell className="py-4">
                  <div className="h-8 w-8 bg-primary/10 rounded animate-pulse" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  return (
    <div className="overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="border-b border-border/50 bg-muted/20">
            <TableHead
              className="cursor-pointer hover:text-primary transition-colors text-charcoal font-medium py-4"
              onClick={() => handleSort('displayName')}
            >
              User
              {sortField === 'displayName' && (
                <span className="ml-1 text-primary">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </TableHead>
            <TableHead
              className="cursor-pointer hover:text-primary transition-colors text-charcoal font-medium py-4"
              onClick={() => handleSort('email')}
            >
              Email
              {sortField === 'email' && (
                <span className="ml-1 text-primary">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </TableHead>
            <TableHead
              className="cursor-pointer hover:text-primary transition-colors text-charcoal font-medium py-4"
              onClick={() => handleSort('role')}
            >
              Role
              {sortField === 'role' && (
                <span className="ml-1 text-primary">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </TableHead>
            <TableHead className="text-charcoal font-medium py-4">Status</TableHead>
            <TableHead
              className="cursor-pointer hover:text-primary transition-colors text-charcoal font-medium py-4"
              onClick={() => handleSort('lastLoginAt')}
            >
              Last Login
              {sortField === 'lastLoginAt' && (
                <span className="ml-1 text-primary">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </TableHead>
            <TableHead className="w-[100px] text-charcoal font-medium py-4">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedUsers.map((user) => (
            <TableRow key={user.uid} className="border-b border-border/30 hover:bg-muted/30 transition-colors">
              <TableCell className="py-4">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10 ring-2 ring-primary/10">
                    <AvatarImage src={user.photoURL || undefined} />
                    <AvatarFallback className="bg-primary/10 text-primary text-sm font-medium">
                      {getInitials(user)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium text-charcoal">
                      {user.displayName || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'No Name'}
                    </div>
                    <div className="text-sm text-grey">
                      {user.firstName && user.lastName
                        ? `${user.firstName} ${user.lastName}`
                        : user.displayName || 'No display name'
                      }
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell className="text-grey py-4">{user.email}</TableCell>
              <TableCell className="py-4">
                <Badge
                  variant="secondary"
                  className={
                    user.role === 'Admin'
                      ? 'bg-primary/10 text-primary border-primary/20 font-medium'
                      : 'bg-accent/10 text-accent border-accent/20 font-medium'
                  }
                >
                  {user.role}
                </Badge>
              </TableCell>
              <TableCell className="py-4">
                <Badge
                  variant="secondary"
                  className={
                    user.emailVerified
                      ? 'bg-emerald-50 text-emerald-700 border-emerald-200 font-medium'
                      : 'bg-red-50 text-red-700 border-red-200 font-medium'
                  }
                >
                  {user.emailVerified ? 'Verified' : 'Unverified'}
                </Badge>
              </TableCell>
              <TableCell className="text-grey py-4">
                {formatDate(user.lastLoginAt)}
              </TableCell>
              <TableCell className="py-4">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 hover:bg-primary/10 hover:text-primary transition-colors"
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-40 bg-white border-border/50 shadow-lg">
                    <DropdownMenuItem
                      onClick={() => onEditUser(user)}
                      className="cursor-pointer hover:bg-primary/5 focus:bg-primary/5 text-charcoal"
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => onDeleteUser(user)}
                      className="cursor-pointer hover:bg-red-50 focus:bg-red-50 text-red-600 focus:text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {users.length === 0 && !loading && (
        <div className="text-center py-12 text-grey">
          <Users className="h-12 w-12 mx-auto mb-4 text-grey/50" />
          <p className="text-lg font-medium text-charcoal mb-2">No users found</p>
          <p className="text-sm">Create your first user to get started.</p>
        </div>
      )}
    </div>
  );
}
