"use client";

import { createContext, useContext, useEffect, useMemo, useState } from "react";
import type { User as FirebaseUser } from "firebase/auth";
import { useAuth } from "@/hooks/useAuth";
import {
  getUserProfile,
  updateUserProfile,
  type UserProfile,
} from "@/Services/userService";

type UserContextValue = {
  user: UserProfile | null;
  authUser: FirebaseUser | null;
  loading: boolean;
  error: string | null;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  refreshProfile: () => Promise<void>;
  isAuthenticated: boolean;
  hasProfile: boolean;
};

const UserContext = createContext<UserContextValue | undefined>(undefined);

export function useUserContext(): UserContextValue {
  const ctx = useContext(UserContext);
  if (!ctx) throw new Error("useUser must be used within <UserProvider>");
  return ctx;
}

export default function UserProvider({ children }: { children: React.ReactNode }) {
  const { user: authUser, loading: authLoading } = useAuth();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;

    async function fetchProfile(currentUser: FirebaseUser) {
      try {
        setError(null);
        setLoading(true);
        const profile = await getUserProfile(currentUser.uid);
        if (!isMounted) return;
        setUserProfile(profile);
      } catch (e) {
        if (!isMounted) return;
        console.error("Error fetching user profile:", e);
        setError(e instanceof Error ? e.message : "Failed to fetch user profile");
      } finally {
        if (!isMounted) return;
        setLoading(false);
      }
    }

    if (authLoading) {
      setLoading(true);
      return () => {
        isMounted = false;
      };
    }

    if (!authUser) {
      setUserProfile(null);
      setLoading(false);
      return () => {
        isMounted = false;
      };
    }

    fetchProfile(authUser);

    return () => {
      isMounted = false;
    };
  }, [authUser, authLoading]);

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!authUser || !userProfile) return;
    try {
      setError(null);
      await updateUserProfile(authUser.uid, updates);
      setUserProfile((prev) => (prev ? { ...prev, ...updates } : prev));
    } catch (e) {
      console.error("Error updating user profile:", e);
      setError(e instanceof Error ? e.message : "Failed to update profile");
      throw e;
    }
  };

  const refreshProfile = async () => {
    if (!authUser) return;
    try {
      setError(null);
      const profile = await getUserProfile(authUser.uid);
      setUserProfile(profile);
    } catch (e) {
      console.error("Error refreshing user profile:", e);
      setError(e instanceof Error ? e.message : "Failed to refresh profile");
    }
  };

  const value = useMemo<UserContextValue>(
    () => ({
      user: userProfile,
      authUser: authUser ?? null,
      loading: authLoading || loading,
      error,
      updateProfile,
      refreshProfile,
      isAuthenticated: !!authUser,
      hasProfile: !!userProfile,
    }),
    [userProfile, authUser, authLoading, loading, error]
  );

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
}


