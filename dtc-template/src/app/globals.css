@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  /* Font CSS variables */
  --font-sans: var(--font-montserrat);
  --font-display-en: "Glancyr", var(--font-montserrat), system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
  --font-sans-en: var(--font-montserrat), system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
  --font-sans-ar: var(--font-noto-sans-arabic), "Greta Text Arabic", "Noto Sans Arabic", system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;

  /* Brand color variables (DTC) */
  --emerald: #026c4a;         /* Primary: Emerald Green */
  --emerald-deep: #0c402e;    /* Primary: Deep Emerald */
  --black: #010101;           /* Primary: Black */
  --charcoal: #272626;        /* Primary: Charcoal Grey */
  --white: #ffffff;           /* Primary: White */

  --grey: #6a6c73;            /* Secondary: Grey */
  --bright-green: #29b06f;    /* Secondary: Bright Green */
  --lime-green: #88c766;      /* Secondary: Lime Green */
}

@font-face {
  font-family: "Glancyr";
  src: url("/fonts/glancyr/Glancyr-Light.otf") format("opentype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Glancyr";
  src: url("/fonts/glancyr/Glancyr-Regular.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Glancyr";
  src: url("/fonts/glancyr/Glancyr-SemiBold.otf") format("opentype");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Glancyr";
  src: url("/fonts/glancyr/Glancyr-Bold.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Greta Text Arabic";
  src: url("/fonts/greta-text-arabic/GretaTextArabic-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-montserrat);

  /* Font families for Tailwind */
  --font-family-sans: var(--font-sans-en);
  --font-family-arabic: var(--font-sans-ar);
  --font-family-display: var(--font-display-en);

  /* Expose brand tokens to Tailwind (usable as bg-primary, text-charcoal, etc.) */
  --color-primary: var(--emerald);
  --color-primary-deep: var(--emerald-deep);
  --color-black: var(--black);
  --color-charcoal: var(--charcoal);
  --color-white: var(--white);
  --color-grey: var(--grey);
  --color-accent: var(--bright-green);
  --color-accent-lime: var(--lime-green);

  /* Semantics */
  --color-border: #e5e7eb; /* subtle gray for elegant borders */
  --color-muted: #f7f7f7;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Default typography */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans-en);
}

/* Font utility classes */
.font-latin {
  font-family: var(--font-sans-en);
}

.font-arabic {
  font-family: var(--font-sans-ar);
}

/* Arabic locale typography */
:lang(ar) body,
html[lang="ar"] body,
.font-arabic {
  font-family: var(--font-sans-ar);
}

/* Display font for headings */
.font-display {
  font-family: var(--font-display-en);
}

/* RTL specific styles */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* RTL Sidebar positioning */
[dir="rtl"] [data-sidebar="sidebar"] {
  direction: rtl;
}

/* RTL Sidebar container positioning */
[dir="rtl"] [data-slot="sidebar-container"] {
  left: auto;
  right: 0;
}

/* RTL Sidebar gap positioning */
[dir="rtl"] [data-slot="sidebar-gap"] {
  order: 1;
}

/* RTL Main content */
[dir="rtl"] [data-slot="sidebar-inset"] {
  margin-left: 0;
  margin-right: auto;
}

[dir="rtl"] .space-x-3 > * + * {
  margin-left: 0;
  margin-right: 0.75rem;
}

[dir="rtl"] .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

/* Better RTL support for gap utility */
[dir="rtl"] .gap-3 {
  gap: 0.75rem;
}

/* Ensure Arabic text renders properly */
:lang(ar) {
  font-family: var(--font-sans-ar) !important;
}

/* Force Arabic font for all Arabic content */
html[lang="ar"] * {
  font-family: var(--font-sans-ar) !important;
}

/* DTC Sidebar Styling */
[data-sidebar="sidebar"] {
  --sidebar-background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-deep) 100%);
  --sidebar-foreground: white;
  --sidebar-primary: var(--color-accent);
  --sidebar-primary-foreground: white;
  --sidebar-accent: rgba(255, 255, 255, 0.1);
  --sidebar-accent-foreground: white;
  --sidebar-border: rgba(255, 255, 255, 0.2);
  --sidebar-ring: rgba(255, 255, 255, 0.3);
}

/* Collapsed (icon) mode — target the real state on the sidebar element */
[data-slot="sidebar"][data-collapsible="icon"] {
  width: var(--sidebar-width-icon) !important;
}

[data-slot="sidebar"][data-collapsible="icon"] [data-slot="sidebar-inner"] {
  overflow: hidden;
}

/* Hide textual content when collapsed */
[data-slot="sidebar"][data-collapsible="icon"] .sidebar-text,
[data-slot="sidebar"][data-collapsible="icon"] .sidebar-user-info {
  display: none !important;
}

/* Center and tighten interactive rows when collapsed */
[data-slot="sidebar"][data-collapsible="icon"] .sidebar-nav-item,
[data-slot="sidebar"][data-collapsible="icon"] .sidebar-footer-btn {
  justify-content: center;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

/* Compact user section when collapsed */
[data-slot="sidebar"][data-collapsible="icon"] .sidebar-user-section {
  padding: 0.5rem 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

[data-slot="sidebar"][data-collapsible="icon"] .sidebar-user-section img,
[data-slot="sidebar"][data-collapsible="icon"] .sidebar-user-section .w-12,
[data-slot="sidebar"][data-collapsible="icon"] .sidebar-user-section .h-12 {
  width: 24px !important;
  height: 24px !important;
}

/* Compact logo when collapsed */
[data-slot="sidebar"][data-collapsible="icon"] .sidebar-logo {
  padding: 0.5rem 0.25rem;
}

[data-slot="sidebar"][data-collapsible="icon"] .sidebar-logo img {
  width: 24px !important;
  height: 24px !important;
}

/* Highlight ring for active icon in minimized mode */
[data-slot="sidebar"][data-collapsible="icon"] .sidebar-nav-btn[data-active="true"],
[data-slot="sidebar"][data-collapsible="icon"] .sidebar-nav-btn:hover {
  box-shadow: 0 0 0 2px rgba(255,255,255,0.18) inset;
}

/* Elegant subtle slide effect for list buttons */
.sidebar-nav-btn { transition: transform 180ms ease, box-shadow 180ms ease, background-color 180ms ease, border-color 180ms ease; }

/* Ensure proper background for dashboard */
.dashboard-layout {
  background: linear-gradient(135deg, var(--color-muted) 0%, rgba(var(--color-primary), 0.05) 100%);
  min-height: 100vh;
}

/* Professional card styling */
.dtc-card {
  background: var(--color-white);
  border: 1px solid var(--color-border);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s ease-in-out;
}

.dtc-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
