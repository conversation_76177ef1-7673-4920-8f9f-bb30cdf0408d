"use client";

import { useUser } from "@/hooks/useUser";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/Card";
import { DtcHero } from "@/components/ui/Hero";

export default function DashboardPage() {
  const { user } = useUser();

  return (
    <div className="space-y-6">
      <DtcHero
        title="Welcome to DTC Portal"
        subtitle={user ? `Hello ${user.displayName || user.email?.split('@')[0]}, welcome to your dashboard.` : undefined}
        image="hero2"
      />

      {/* Content Area */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="border-border shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="text-charcoal font-display">Quick Stats</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-grey">Dashboard content coming soon...</p>
          </CardContent>
        </Card>

        <Card className="border-border shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="text-charcoal font-display">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-grey">Activity feed coming soon...</p>
          </CardContent>
        </Card>

        <Card className="border-border shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="text-charcoal font-display">System Status</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-grey">System monitoring coming soon...</p>
          </CardContent>
        </Card>
      </div>

      {/* Additional Content */}
      <Card className="border-border shadow-sm">
        <CardHeader>
          <CardTitle className="text-charcoal font-display">Getting Started</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-grey">
            This is your DTC Portal dashboard. Use the sidebar to navigate between different sections.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-primary/5 rounded-lg border border-primary/20">
              <h3 className="font-semibold text-charcoal mb-2">Home</h3>
              <p className="text-sm text-grey">
                Your main dashboard with overview and quick access to key features.
              </p>
            </div>
            {user?.role === "Admin" && (
              <div className="p-4 bg-accent/5 rounded-lg border border-accent/20">
                <h3 className="font-semibold text-charcoal mb-2">User Management</h3>
                <p className="text-sm text-grey">
                  Manage users, roles, and permissions (Admin only).
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
