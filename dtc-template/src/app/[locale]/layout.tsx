import type { Metadata } from "next";
import "../globals.css";
import AuthProvider from "@/app/Firebase/Authentication/AuthProvider";
import UserProvider from "@/app/Firebase/Authentication/UserProvider";
import SetupGate from "@/app/Firebase/Authentication/SetupGate";
import { montserrat, notoSansArabic } from "@/styles/fonts";
import { NextIntlClientProvider } from "next-intl";
import { getDirection, type Locale } from "@/i18n/config";
import { notFound } from "next/navigation";
import { cn } from "@/lib/utils";
import { Toaster } from "@/components/ui/sonner";

export const metadata: Metadata = {
  title: "DTC Portal",
  description: "Digital Transformation Center Portal",
};

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  const { locale } = await params;
  
  // Validate that the incoming locale is valid
  const validLocales = ["en", "ar"];
  if (!validLocales.includes(locale)) {
    notFound();
  }

  const messages = (await import(`@/i18n/messages/${locale}.json`)).default;
  const dir = getDirection(locale as Locale);

  // Apply appropriate font classes based on locale
  const fontClasses = cn(
    montserrat.variable,
    notoSansArabic.variable,
    "antialiased",
    locale === "ar" ? "font-arabic" : "font-latin"
  );

  return (
    <html lang={locale} dir={dir}>
      <body className={fontClasses}>
        <NextIntlClientProvider
          messages={messages}
          locale={locale}
          timeZone={Intl.DateTimeFormat().resolvedOptions().timeZone}
        >
          <AuthProvider>
            <UserProvider>
              <SetupGate>{children}</SetupGate>
              <Toaster />
            </UserProvider>
          </AuthProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
