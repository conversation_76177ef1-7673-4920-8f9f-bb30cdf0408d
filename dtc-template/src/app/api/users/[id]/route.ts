import { NextRequest, NextResponse } from 'next/server';
import { getUserProfile, updateUserProfile, deleteUserProfile, type UserProfile } from '@/Services/userService';

// GET /api/users/[id] - Get a specific user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const user = await getUserProfile(id);
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ user }, { status: 200 });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// PUT /api/users/[id] - Update a user
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    
    // Check if user exists
    const existingUser = await getUserProfile(id);
    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Prepare update data (exclude uid, createdAt, updatedAt)
    const updateData: Partial<UserProfile> = {};
    
    const allowedFields = [
      'email',
      'displayName',
      'photoURL',
      'firstName',
      'lastName',
      'phoneNumber',
      'role',
      'emailVerified',
      'preferences'
    ];

    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updateData[field as keyof UserProfile] = body[field];
      }
    });

    // Update user profile
    await updateUserProfile(id, updateData);
    
    // Fetch updated user
    const updatedUser = await getUserProfile(id);

    return NextResponse.json(
      { 
        message: 'User updated successfully',
        user: updatedUser
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE /api/users/[id] - Delete a user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    // Check if user exists
    const existingUser = await getUserProfile(id);
    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Delete user profile from Firestore
    await deleteUserProfile(id);

    // Note: We're only deleting from Firestore here
    // Firebase Auth user deletion would require Firebase Admin SDK
    // which needs server-side setup with service account credentials

    return NextResponse.json(
      { message: 'User deleted successfully from database' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
