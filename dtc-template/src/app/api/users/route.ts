import { NextRequest, NextResponse } from 'next/server';
import { getAllUsers, createUserProfile, type UserProfile } from '@/Services/userService';
import { emailPasswordSignUp } from '@/Services/authService';

// GET /api/users - Get all users
export async function GET() {
  try {
    const users = await getAllUsers();
    return NextResponse.json({ users }, { status: 200 });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST /api/users - Create a new user
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      email,
      password,
      firstName,
      lastName,
      displayName,
      photoURL,
      role = 'DTC',
      phoneNumber,
    } = body;

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Create Firebase Auth user first
    let firebaseUser;
    try {
      firebaseUser = await emailPasswordSignUp(
        { email, password },
        displayName || `${firstName} ${lastName}`.trim()
      );
    } catch (authError: any) {
      console.error('Error creating Firebase user:', authError);
      return NextResponse.json(
        { error: `Failed to create user account: ${authError.message}` },
        { status: 400 }
      );
    }

    // Create user profile in Firestore
    const userProfileData: Omit<UserProfile, 'uid' | 'createdAt' | 'updatedAt'> = {
      email,
      displayName: displayName || `${firstName} ${lastName}`.trim() || null,
      photoURL: photoURL || null,
      firstName: firstName || null,
      lastName: lastName || null,
      phoneNumber: phoneNumber || null,
      emailVerified: false,
      provider: 'email',
      role: role as 'Admin' | 'DTC',
      lastLoginAt: new Date(),
    };

    // Update the user profile with the actual Firebase UID
    const updatedProfileData = {
      ...userProfileData,
      uid: firebaseUser.uid,
    };

    // Create the user profile using the Firebase UID
    const { createDoc, readDoc } = await import('@/Services/firestoreService');
    const userPath = `users/${firebaseUser.uid}`;
    
    await createDoc(userPath, updatedProfileData);
    const createdUser = await readDoc<UserProfile>(userPath);

    return NextResponse.json(
      { 
        message: 'User created successfully',
        user: createdUser
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}
