import { type User } from "firebase/auth";
import { type Timestamp } from "firebase/firestore";
import { createDoc, readDoc, updateDocFields } from "./firestoreService";

// User data structure for Firestore
export interface UserProfile {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  firstName?: string | null;
  lastName?: string | null;
  phoneNumber?: string | null;
  emailVerified: boolean;
  provider: 'google' | 'email' | 'phone';
  role: 'Admin' | 'DTC';
  createdAt?: Timestamp | Date; // Firestore timestamp
  updatedAt?: Timestamp | Date; // Firestore timestamp
  lastLoginAt?: Timestamp | Date; // Firestore timestamp
  preferences?: {
    language?: string;
    theme?: string;
    notifications?: boolean;
  };
}

// Extract user info from Firebase Auth User
export function extractUserInfo(user: User, provider: 'google' | 'email' | 'phone' = 'email'): Omit<UserProfile, 'createdAt' | 'updatedAt'> {
  const { uid, email, displayName, photoURL, emailVerified } = user;
  
  // For Google sign-in, try to extract first and last name from displayName
  let firstName: string | null = null;
  let lastName: string | null = null;
  
  if (displayName && provider === 'google') {
    const nameParts = displayName.split(' ');
    firstName = nameParts[0] || null;
    lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : null;
  }
  
  return {
    uid,
    email,
    displayName,
    photoURL,
    firstName,
    lastName,
    phoneNumber: user.phoneNumber,
    emailVerified,
    provider,
    role: provider === 'google' ? 'Admin' : 'DTC', // Google sign-in users get Admin role by default
    lastLoginAt: new Date(),
  };
}

// Create or update user profile in Firestore
export async function createOrUpdateUserProfile(user: User, provider: 'google' | 'email' | 'phone' = 'email'): Promise<UserProfile> {
  const userPath = `users/${user.uid}`;
  
  // Check if user already exists
  const existingUser = await readDoc<UserProfile>(userPath);
  
  if (existingUser) {
    // Update existing user with latest info and last login time
    const updateData = {
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      emailVerified: user.emailVerified,
      lastLoginAt: new Date(),
    };
    
    await updateDocFields(userPath, updateData);
    return { ...existingUser, ...updateData };
  } else {
    // Create new user profile
    const userInfo = extractUserInfo(user, provider);
    const newUserProfile: Omit<UserProfile, 'createdAt' | 'updatedAt'> = {
      ...userInfo,
      preferences: {
        language: 'en',
        theme: 'light',
        notifications: true,
      },
    };
    
    await createDoc(userPath, newUserProfile);
    
    // Return the created user (createDoc adds timestamps)
    const createdUser = await readDoc<UserProfile>(userPath);
    return createdUser!;
  }
}

// Get user profile from Firestore
export async function getUserProfile(uid: string): Promise<UserProfile | null> {
  return await readDoc<UserProfile>(`users/${uid}`);
}

// Update user profile
export async function updateUserProfile(uid: string, updates: Partial<UserProfile>): Promise<void> {
  await updateDocFields(`users/${uid}`, updates);
}

// Update user preferences
export async function updateUserPreferences(uid: string, preferences: Partial<UserProfile['preferences']>): Promise<void> {
  const currentUser = await getUserProfile(uid);
  if (currentUser) {
    const updatedPreferences = { ...currentUser.preferences, ...preferences };
    await updateDocFields(`users/${uid}`, { preferences: updatedPreferences });
  }
}

// Get all users from Firestore
export async function getAllUsers(): Promise<UserProfile[]> {
  const { readCollection } = await import('./firestoreService');
  return await readCollection<UserProfile>('users');
}

// Delete user profile from Firestore
export async function deleteUserProfile(uid: string): Promise<void> {
  const { deleteDocByPath } = await import('./firestoreService');
  await deleteDocByPath(`users/${uid}`);
}

// Create user with custom data (for admin creation)
export async function createUserProfile(userData: Omit<UserProfile, 'uid' | 'createdAt' | 'updatedAt'>): Promise<UserProfile> {
  const { createDoc, readDoc } = await import('./firestoreService');

  // Generate a temporary UID for the user profile
  const tempUid = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const userPath = `users/${tempUid}`;

  const newUserProfile = {
    ...userData,
    uid: tempUid,
    preferences: {
      language: 'en',
      theme: 'light',
      notifications: true,
      ...userData.preferences,
    },
  };

  await createDoc(userPath, newUserProfile);

  // Return the created user
  const createdUser = await readDoc<UserProfile>(userPath);
  return createdUser!;
}
